<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Musik - Jana B<PERSON>it<PERSON></title>
    <link rel="icon" type="image/png" href="bilder/favicon.png">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="musik-styles.css">
</head>
<body>
    <!-- Header wird durch header-and-footer.js eingefügt -->
    
    <!-- Breadcrumb Navigation -->
    <div class="breadcrumb-container">
        <div class="container">
            <nav class="breadcrumb">
                <a href="index.html" class="breadcrumb-link">Home</a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-current">Musik</span>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <main class="musik-main">
        <div class="container">
            <header class="musik-header">
                <h1 class="musik-title">Musik</h1>
            </header>


            <!-- Mobile Layout -->
            <div class="mobile-layout">
                <!-- Song List Container -->
                <div class="song-list-container">
                    <!-- Mobile Filter Toggle Button -->
                    <div class="mobile-filter-toggle-container">
                        <button id="mobile-filter-toggle" class="mobile-filter-toggle-btn">
                            <span>Playlist filtern</span>
                            <svg class="filter-toggle-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="6,9 12,15 18,9"/>
                            </svg>
                        </button>
                    </div>

                <!-- Mobile Filter Controls (initially hidden) -->
                <div class="mobile-filter-controls" id="mobile-filter-controls" style="display: none;">
                    <!-- Mobile Filter verwenden die gleiche Struktur wie Desktop -->
                    <div class="filter-group">
                        <label class="filter-label">Genres</label>
                        <div class="filter-dropdown" id="mobile-genre-filter-dropdown">
                            <button class="filter-dropdown-btn" id="mobile-genre-filter-btn">
                                <span class="filter-text">Alle Genres</span>
                                <svg class="filter-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="6,9 12,15 18,9"/>
                                </svg>
                            </button>
                            <div class="filter-dropdown-content" id="mobile-genre-filter-content">
                                <div class="filter-options" id="mobile-genre-filter-options">
                                    <!-- Optionen werden dynamisch hinzugefügt -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="filter-group">
                        <label class="filter-label">Sprachen</label>
                        <div class="filter-dropdown" id="mobile-sprache-filter-dropdown">
                            <button class="filter-dropdown-btn" id="mobile-sprache-filter-btn">
                                <span class="filter-text">Alle Sprachen</span>
                                <svg class="filter-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="6,9 12,15 18,9"/>
                                </svg>
                            </button>
                            <div class="filter-dropdown-content" id="mobile-sprache-filter-content">
                                <div class="filter-options" id="mobile-sprache-filter-options">
                                    <!-- Optionen werden dynamisch hinzugefügt -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="filter-group">
                        <label class="filter-label">Alben</label>
                        <div class="filter-dropdown" id="mobile-album-filter-dropdown">
                            <button class="filter-dropdown-btn" id="mobile-album-filter-btn">
                                <span class="filter-text">Alle Alben</span>
                                <svg class="filter-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="6,9 12,15 18,9"/>
                                </svg>
                            </button>
                            <div class="filter-dropdown-content" id="mobile-album-filter-content">
                                <div class="filter-options" id="mobile-album-filter-options">
                                    <!-- Optionen werden dynamisch hinzugefügt -->
                                </div>
                            </div>
                        </div>

                        <!-- Mobile Filter Buttons -->
                        <div class="filter-apply-group">
                            <button class="filter-apply-btn mobile-filter-apply-btn" id="mobile-apply-filters-btn" title="Filter anwenden">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="20,6 9,17 4,12"/>
                                </svg>
                                <span>Anwenden</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Mobile Control Buttons -->
                <div class="mobile-control-buttons">
                    <button class="mobile-control-btn" id="mobile-play-filtered-btn" title="Wiedergabe">
                        <svg class="play-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8 5v14l11-7z"/>
                        </svg>
                        <svg class="pause-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                        </svg>
                    </button>
                    <button class="mobile-control-btn" id="mobile-prev-btn" title="Vorheriges Lied">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"/>
                        </svg>
                    </button>
                    <button class="mobile-control-btn" id="mobile-next-btn" title="Nächstes Lied">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
                        </svg>
                    </button>
                    <button class="mobile-control-btn" id="mobile-shuffle-btn" title="Zufällige Wiedergabe">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="16,3 21,3 21,8"/>
                            <path d="M4 20L21 3"/>
                            <polyline points="21,16 21,21 16,21"/>
                            <path d="M15 15l6 6"/>
                            <path d="M4 4l5 5"/>
                        </svg>
                    </button>
                    <button class="mobile-control-btn" id="mobile-repeat-btn" title="Wiederholen">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="17,1 21,5 17,9"/>
                            <path d="M3 11V9a4 4 0 0 1 4-4h14"/>
                            <polyline points="7,23 3,19 7,15"/>
                            <path d="M21 13v2a4 4 0 0 1-4 4H3"/>
                        </svg>
                    </button>
                    <button class="mobile-control-btn" id="mobile-download-filtered-btn" title="Gefilterte Liste herunterladen">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                            <polyline points="7,10 12,15 17,10"/>
                            <line x1="12" y1="15" x2="12" y2="3"/>
                        </svg>
                    </button>

                </div>

                    <!-- Filter Status Display -->
                    <div class="filter-status" id="filter-status" style="display: none;">
                        <span class="filter-status-text" id="filter-status-text"></span>
                        <button class="filter-status-reset" id="filter-status-reset" title="Filter zurücksetzen">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"/>
                                <line x1="6" y1="6" x2="18" y2="18"/>
                            </svg>
                        </button>
                    </div>

                    <!-- Mobile Song List -->
                    <div class="mobile-song-list" id="mobile-song-list">
                        <!-- Songs werden hier dynamisch eingefügt -->
                    </div>
                </div> <!-- Ende song-list-container -->

                <!-- Desktop Sidebar (nur auf Desktop sichtbar) -->
                <div class="desktop-sidebar" id="desktop-sidebar">
                    <!-- Desktop Player Content (gleicher Inhalt wie Modal) -->
                    <div class="desktop-player-content">
                        <!-- Upper Section: Album Cover with Tab Content -->
                        <div class="desktop-upper-section">
                            <!-- Tab Content Area -->
                            <div class="desktop-tab-content-area">
                                <!-- Album Cover Tab -->
                                <div class="desktop-tab-content active" data-content="cover">
                                    <!-- Cover wird als Hintergrund gesetzt -->
                                </div>

                                <!-- Lyrics Tab -->
                                <div class="desktop-tab-content" data-content="lyrics" id="desktop-lyrics-content">
                                    <p class="no-lyrics">Wähle ein Lied aus, um die Lyrics anzuzeigen.</p>
                                </div>

                                <!-- Translation Tab -->
                                <div class="desktop-tab-content" data-content="translation" id="desktop-lyrics-translation">
                                    <p class="no-lyrics">Deutsche Übersetzung wird hier angezeigt.</p>
                                </div>

                                <!-- Info Tab -->
                                <div class="desktop-tab-content" data-content="info" id="desktop-info-content">
                                    <div class="info-container">
                                        <h3 class="tab-content-title">Info</h3>
                                        <div class="info-item">
                                            <span class="info-label">Titel:</span>
                                            <span class="info-value" id="desktop-info-title">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Künstlerin:</span>
                                            <span class="info-value" id="desktop-info-artist">Jana Breitmar</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Album:</span>
                                            <span class="info-value clickable" id="desktop-info-album" data-filter-type="album">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Genre:</span>
                                            <span class="info-value clickable" id="desktop-info-genre" data-filter-type="genre">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Sprache:</span>
                                            <span class="info-value clickable" id="desktop-info-language" data-filter-type="sprache">-</span>
                                        </div>

                                        <!-- Interpretation als Info-Item -->
                                        <div class="info-item" id="desktop-info-interpretation" style="display: none;">
                                            <span class="info-label">Interpretation:</span>
                                            <span class="info-value interpretation-text">-</span>
                                        </div>

                                        <!-- Widmung als Info-Item -->
                                        <div class="info-item" id="desktop-info-widmung" style="display: none;">
                                            <span class="info-label">Widmung:</span>
                                            <span class="info-value widmung-text">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Tab Navigation -->
                            <div class="desktop-tab-navigation">
                                <div class="desktop-tab-indicator"></div>
                                <button class="desktop-tab active" data-tab="cover">Cover</button>
                                <button class="desktop-tab" data-tab="lyrics">Lyrics</button>
                                <button class="desktop-tab" data-tab="translation" style="display: none;">Deutsch</button>
                                <button class="desktop-tab" data-tab="info">Info</button>
                            </div>
                        </div>

                        <!-- Lower Section: Player Controls -->
                        <div class="desktop-lower-section">
                            <!-- Song Title -->
                            <div class="desktop-song-title-container">
                                <h3 class="desktop-song-title" id="desktop-song-title">Wähle ein Lied aus</h3>
                                <p class="desktop-song-artist" id="desktop-song-artist">Jana Breitmar</p>
                            </div>

                            <!-- Player Controls -->
                            <div class="desktop-player-controls">
                                <div class="desktop-control-buttons">
                                    <button class="desktop-control-btn" id="desktop-prev-btn" title="Vorheriges Lied">
                                        <svg viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"/>
                                        </svg>
                                    </button>
                                    <button class="desktop-control-btn desktop-play-pause-btn" id="desktop-play-pause-btn" title="Abspielen/Pausieren">
                                        <svg class="play-icon" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M8 5v14l11-7z"/>
                                        </svg>
                                        <svg class="pause-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                                            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                                        </svg>
                                    </button>
                                    <button class="desktop-control-btn" id="desktop-next-btn" title="Nächstes Lied">
                                        <svg viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
                                        </svg>
                                    </button>
                                </div>

                                <div class="desktop-progress-container">
                                    <span class="desktop-time-display" id="desktop-current-time">0:00</span>
                                    <div class="desktop-progress-bar-container">
                                        <div class="desktop-progress-bar" id="desktop-progress-bar">
                                            <div class="desktop-progress-fill" id="desktop-progress-fill"></div>
                                            <div class="desktop-progress-handle" id="desktop-progress-handle"></div>
                                        </div>
                                    </div>
                                    <span class="desktop-time-display" id="desktop-total-time">0:00</span>
                                </div>

                                <div class="desktop-additional-controls">
                                    <button class="desktop-control-btn" id="desktop-shuffle-btn" title="Zufällige Wiedergabe">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <polyline points="16,3 21,3 21,8"/>
                                            <path d="M4 20L21 3"/>
                                            <polyline points="21,16 21,21 16,21"/>
                                            <path d="M15 15l6 6"/>
                                            <path d="M4 4l5 5"/>
                                        </svg>
                                    </button>
                                    <button class="desktop-control-btn" id="desktop-repeat-btn" title="Wiederholen">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <polyline points="17,1 21,5 17,9"/>
                                            <path d="M3 11V9a4 4 0 0 1 4-4h14"/>
                                            <polyline points="7,23 3,19 7,15"/>
                                            <path d="M21 13v2a4 4 0 0 1-4 4H3"/>
                                        </svg>
                                    </button>
                                    <button class="desktop-control-btn" id="desktop-download-current-btn" title="Aktuelles Lied herunterladen">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                            <polyline points="7,10 12,15 17,10"/>
                                            <line x1="12" y1="15" x2="12" y2="3"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mini Player (wird am unteren Rand angezeigt) -->
                <div class="mini-player" id="mini-player" style="display: none;">
                    <div class="mini-player-info" id="mini-player-info">
                        <div class="mini-album-cover">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                            </svg>
                        </div>
                        <div class="mini-song-info">
                            <span class="mini-song-title" id="mini-song-title">Kein Lied ausgewählt</span>
                            <span class="mini-artist">Jana Breitmar</span>
                        </div>
                    </div>
                    <div class="mini-controls">
                        <button class="mini-control-btn" id="mini-play-pause" title="Abspielen/Pausieren">
                            <svg class="play-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M8 5v14l11-7z"/>
                            </svg>
                            <svg class="pause-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                                <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                            </svg>
                        </button>
                        <button class="mini-control-btn" id="mini-expand" title="Player öffnen">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="15,3 21,3 21,9"/>
                                <polyline points="9,21 3,21 3,15"/>
                                <line x1="21" y1="3" x2="14" y2="10"/>
                                <line x1="3" y1="21" x2="10" y2="14"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Mobile Player Modal -->
    <div class="modal-overlay" id="modal-overlay" style="display: none;">
        <div class="modal-player" id="modal-player">
            <!-- Close Button (overlapping album cover) -->
            <button class="modal-close" id="modal-close">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"/>
                    <line x1="6" y1="6" x2="18" y2="18"/>
                </svg>
            </button>

            <!-- Upper Half: Album Cover with Tab Content -->
            <div class="modal-upper-section">
                <!-- Tab Content Area -->
                <div class="modal-tab-content-area">
                    <!-- Album Cover Tab -->
                    <div class="modal-tab-content active" data-content="cover">
                        <!-- Cover wird als Hintergrund gesetzt -->
                    </div>

                    <!-- Lyrics Tab -->
                    <div class="modal-tab-content" data-content="lyrics" id="modal-lyrics-content">
                        <p class="no-lyrics">Wähle ein Lied aus, um die Lyrics anzuzeigen.</p>
                    </div>

                    <!-- Translation Tab -->
                    <div class="modal-tab-content" data-content="translation" id="modal-lyrics-translation">
                        <p class="no-lyrics">Deutsche Übersetzung wird hier angezeigt.</p>
                    </div>

                    <!-- Info Tab -->
                    <div class="modal-tab-content" data-content="info" id="modal-info-content">
                        <div class="info-container">
                            <h3 class="tab-content-title">Info</h3>
                            <div class="info-item">
                                <span class="info-label">Titel:</span>
                                <span class="info-value" id="modal-info-title">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Künstlerin:</span>
                                <span class="info-value" id="modal-info-artist">Jana Breitmar</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Album:</span>
                                <span class="info-value clickable" id="modal-info-album" data-filter-type="album">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Genre:</span>
                                <span class="info-value clickable" id="modal-info-genre" data-filter-type="genre">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Sprache:</span>
                                <span class="info-value clickable" id="modal-info-language" data-filter-type="sprache">-</span>
                            </div>

                            <!-- Interpretation als Info-Item -->
                            <div class="info-item" id="modal-info-interpretation" style="display: none;">
                                <span class="info-label">Interpretation:</span>
                                <span class="info-value interpretation-text">-</span>
                            </div>

                            <!-- Widmung als Info-Item -->
                            <div class="info-item" id="modal-info-widmung" style="display: none;">
                                <span class="info-label">Widmung:</span>
                                <span class="info-value widmung-text">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tab Navigation -->
                <div class="modal-tab-navigation">
                    <div class="modal-tab-indicator"></div>
                    <button class="modal-tab active" data-tab="cover">Cover</button>
                    <button class="modal-tab" data-tab="lyrics">Lyrics</button>
                    <button class="modal-tab" data-tab="translation" style="display: none;">Deutsch</button>
                    <button class="modal-tab" data-tab="info">Info</button>
                </div>
            </div>

            <!-- Lower Half: Player Controls -->
            <div class="modal-lower-section">
                <!-- Song Title -->
                <div class="modal-song-title-container">
                    <h3 class="modal-song-title" id="modal-song-title">Wähle ein Lied aus</h3>
                    <p class="modal-song-artist" id="modal-song-artist">Jana Breitmar</p>
                </div>

                <!-- Player Controls -->
                <div class="modal-player-controls">
                    <div class="modal-control-buttons">
                        <button class="modal-control-btn" id="modal-prev-btn" title="Vorheriges Lied">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"/>
                            </svg>
                        </button>
                        <button class="modal-control-btn modal-play-pause-btn" id="modal-play-pause-btn" title="Abspielen/Pausieren">
                            <svg class="play-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M8 5v14l11-7z"/>
                            </svg>
                            <svg class="pause-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                                <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                            </svg>
                        </button>
                        <button class="modal-control-btn" id="modal-next-btn" title="Nächstes Lied">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
                            </svg>
                        </button>
                    </div>

                    <div class="modal-progress-container">
                        <span class="modal-time-display" id="modal-current-time">0:00</span>
                        <div class="modal-progress-bar-container">
                            <div class="modal-progress-bar" id="modal-progress-bar">
                                <div class="modal-progress-fill" id="modal-progress-fill"></div>
                                <div class="modal-progress-handle" id="modal-progress-handle"></div>
                            </div>
                        </div>
                        <span class="modal-time-display" id="modal-total-time">0:00</span>
                    </div>

                    <div class="modal-additional-controls">
                        <button class="modal-control-btn" id="modal-shuffle-btn" title="Zufällige Wiedergabe">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="16,3 21,3 21,8"/>
                                <path d="M4 20L21 3"/>
                                <polyline points="21,16 21,21 16,21"/>
                                <path d="M15 15l6 6"/>
                                <path d="M4 4l5 5"/>
                            </svg>
                        </button>
                        <button class="modal-control-btn" id="modal-repeat-btn" title="Wiederholen">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="17,1 21,5 17,9"/>
                                <path d="M3 11V9a4 4 0 0 1 4-4h14"/>
                                <polyline points="7,23 3,19 7,15"/>
                                <path d="M21 13v2a4 4 0 0 1-4 4H3"/>
                            </svg>
                        </button>
                        <button class="modal-control-btn" id="modal-download-current-btn" title="Aktuelles Lied herunterladen">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                <polyline points="7,10 12,15 17,10"/>
                                <line x1="12" y1="15" x2="12" y2="3"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Audio Element -->
    <audio id="audio-player" preload="metadata"></audio>

    <!-- Scripts -->
    <script src="header-and-footer.js"></script>
    <script src="musik-data.js"></script>
    <script src="musik-player.js"></script>
</body>
</html>
