/* Musik-Seite Spezifische Styles */

/* Breadcrumb Navigation */
.breadcrumb-container {
    background: #fff8ef;
    padding: 20px 0;
    border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;
}

.breadcrumb-link {
    color: #4b879a;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-link:hover {
    color: #112736;
}

.breadcrumb-separator {
    margin: 0 10px;
    color: #999;
}

.breadcrumb-current {
    color: #112736;
    font-weight: 600;
}

/* Main Layout */
.musik-main {
    padding: 40px 0;
    min-height: calc(100vh - 200px);
}

.musik-header {
    text-align: center;
    margin-bottom: 40px;
}

.musik-title {
    font-size: 3rem;
    font-weight: 700;
    color: #112736;
    margin-bottom: 20px;
}

.musik-description {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}



/* Filter Controls */
.filter-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;
}

.filter-select {
    padding: 8px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    color: #112736;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #4b879a;
}

.reset-button {
    padding: 8px 16px;
    background: #4b879a;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.reset-button:hover {
    background: #112736;
}







/* Mobile Layout - jetzt für alle Bildschirmgrößen */
.mobile-layout {
    display: block;
    overflow: visible;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Desktop Grid Layout */
@media (min-width: 769px) {
    .mobile-layout {
        display: grid !important;
        grid-template-columns: 1fr 400px !important;
        gap: 30px;
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 40px;
        align-items: start;
    }

    .song-list-container {
        min-width: 0;
        width: 100%;
        overflow: visible;
    }

    .desktop-sidebar {
        width: 100%;
    }
}

/* Desktop Sidebar */
.desktop-sidebar {
    display: none; /* Auf Mobile versteckt */
}

@media (min-width: 769px) {
    .desktop-sidebar {
        display: block;
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: sticky;
        top: 20px;
        height: fit-content;
        max-height: calc(100vh - 40px);
    }
}

/* Desktop Player Content */
.desktop-player-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.desktop-upper-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 400px;
}

.desktop-lower-section {
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

/* Desktop Tab Content Area */
.desktop-tab-content-area {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.desktop-tab-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
    overflow-y: auto;
    padding: 20px;
}

.desktop-tab-content.active {
    opacity: 1;
    visibility: visible;
}

.desktop-tab-content[data-content="cover"] {
    padding: 10px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Desktop Lyrics and Translation Tabs - Petrol-Blau Farbverlauf */
.desktop-tab-content[data-content="lyrics"],
.desktop-tab-content[data-content="translation"] {
    background: linear-gradient(135deg, #4b879a 0%, #3a6b7a 25%, #2c5f6f 50%, #1e4a56 75%, #0f3540 100%);
    justify-content: flex-start;
    padding: 30px;
    overflow-y: auto;
}

/* Desktop Tab Content Titel - Einfaches Design */
.desktop-tab-content .tab-content-title {
    margin: 0 0 25px 0;
    padding: 0;
    font-size: 22px;
    font-weight: 700;
    color: white;
    text-align: center;
}

/* Desktop Lyrics und Translation Text - Website-Style */
.desktop-tab-content[data-content="lyrics"] p,
.desktop-tab-content[data-content="translation"] p {
    color: white;
    font-size: 18px;
    line-height: 1.6;
    text-align: center;
    margin: 0;
    padding: 0;
    font-weight: 400;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    white-space: pre-line;
}

/* Desktop Keine Lyrics/Übersetzung verfügbar - Styling */
.desktop-tab-content[data-content="lyrics"] .no-lyrics,
.desktop-tab-content[data-content="translation"] .no-lyrics {
    color: rgba(255, 255, 255, 0.8);
    font-style: italic;
    font-size: 16px;
    text-align: center;
    margin-top: 50px;
}

/* Desktop Info Tab - Petrol-Blau Farbverlauf, kompakter */
.desktop-tab-content[data-content="info"] {
    background: linear-gradient(135deg, #4b879a 0%, #3a6b7a 25%, #2c5f6f 50%, #1e4a56 75%, #0f3540 100%);
    justify-content: flex-start;
    align-items: center;
    padding: 25px;
    overflow-y: auto;
}

/* Desktop Info Container für zentrierte Anordnung */
.desktop-tab-content[data-content="info"] .info-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    max-width: 300px;
    width: 100%;
    margin-top: 0;
}

/* Desktop Info Tab Titel */
.desktop-tab-content[data-content="info"] .tab-content-title {
    color: white;
    margin-bottom: 20px;
    text-align: center;
    width: 100%;
}

/* Desktop Info-Items - Kompakteres Design */
.desktop-tab-content[data-content="info"] .info-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.desktop-tab-content[data-content="info"] .info-item:last-child {
    border-bottom: none;
}

.desktop-tab-content[data-content="info"] .info-label {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    font-size: 15px;
    min-width: 110px;
}

.desktop-tab-content[data-content="info"] .info-value {
    color: white;
    font-size: 15px;
    text-align: right;
    flex: 1;
    margin-left: 15px;
    word-break: break-word;
    line-height: 1.4;
}

/* Desktop Klickbare Info-Values (Album, Genre, Sprache) */
.desktop-tab-content[data-content="info"] .info-value.clickable {
    background: rgba(255, 255, 255, 0.1);
    padding: 6px 12px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
    margin-left: 10px;
}

.desktop-tab-content[data-content="info"] .info-value.clickable:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-1px);
}

/* Desktop Interpretation als Info-Item - Spezielles Styling */
.desktop-tab-content[data-content="info"] .interpretation-text {
    color: white;
    font-size: 14px;
    line-height: 1.5;
    text-align: left;
    word-break: break-word;
    max-width: 100%;
    margin-left: 0;
}

/* Desktop Spezielle Behandlung für Interpretation-Items */
.desktop-tab-content[data-content="info"] .info-item:has(.interpretation-text) {
    flex-direction: column;
    align-items: flex-start;
}

.desktop-tab-content[data-content="info"] .info-item:has(.interpretation-text) .info-label {
    margin-bottom: 8px;
}

/* Desktop Scrollbar Styling */
.desktop-tab-content[data-content="lyrics"]::-webkit-scrollbar,
.desktop-tab-content[data-content="translation"]::-webkit-scrollbar,
.desktop-tab-content[data-content="info"]::-webkit-scrollbar {
    width: 6px;
}

.desktop-tab-content[data-content="lyrics"]::-webkit-scrollbar-track,
.desktop-tab-content[data-content="translation"]::-webkit-scrollbar-track,
.desktop-tab-content[data-content="info"]::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.desktop-tab-content[data-content="lyrics"]::-webkit-scrollbar-thumb,
.desktop-tab-content[data-content="translation"]::-webkit-scrollbar-thumb,
.desktop-tab-content[data-content="info"]::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
}

.desktop-tab-content[data-content="lyrics"]::-webkit-scrollbar-thumb:hover,
.desktop-tab-content[data-content="translation"]::-webkit-scrollbar-thumb:hover,
.desktop-tab-content[data-content="info"]::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
}

/* Desktop Tab Navigation */
.desktop-tab-navigation {
    display: flex;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    position: relative;
}

.desktop-tab {
    flex: 1;
    padding: 12px 16px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: color 0.3s ease;
    position: relative;
}

.desktop-tab.active {
    color: #4b879a;
}

.desktop-tab-indicator {
    position: absolute;
    bottom: 0;
    height: 3px;
    background: #4b879a;
    transition: all 0.3s ease;
    border-radius: 3px 3px 0 0;
}

/* Desktop Song Title */
.desktop-song-title-container {
    text-align: center;
    margin-bottom: 20px;
}

.desktop-song-title {
    font-size: 18px;
    font-weight: 600;
    color: #112736;
    margin: 0 0 4px 0;
}

.desktop-song-artist {
    font-size: 14px;
    color: #666;
    margin: 0;
}

/* Desktop Player Controls */
.desktop-player-controls {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.desktop-control-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
}

.desktop-control-btn {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #112736;
}

.desktop-control-btn:hover {
    background: #e9ecef;
    border-color: #4b879a;
    color: #4b879a;
}

.desktop-control-btn.active {
    background: #4b879a;
    border-color: #4b879a;
    color: white;
}

.desktop-play-pause-btn {
    width: 60px;
    height: 60px;
    background: #4b879a;
    border-color: #4b879a;
    color: white;
}

.desktop-play-pause-btn:hover {
    background: #3a6b7a;
    border-color: #3a6b7a;
}

.desktop-control-btn svg {
    width: 20px;
    height: 20px;
}

.desktop-play-pause-btn svg {
    width: 24px;
    height: 24px;
}

/* Desktop Progress Bar */
.desktop-progress-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.desktop-time-display {
    font-size: 12px;
    color: #666;
    min-width: 35px;
    text-align: center;
}

.desktop-progress-bar-container {
    flex: 1;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    cursor: pointer;
    position: relative;
}

.desktop-progress-bar {
    width: 100%;
    height: 100%;
    position: relative;
}

.desktop-progress-fill {
    height: 100%;
    background: #4b879a;
    border-radius: 3px;
    width: 0%;
    transition: width 0.1s ease;
}

.desktop-progress-handle {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 14px;
    height: 14px;
    background: #4b879a;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease;
    left: 0%;
}

.desktop-progress-bar-container:hover .desktop-progress-handle {
    opacity: 1;
}

/* Desktop Additional Controls */
.desktop-additional-controls {
    display: flex;
    justify-content: center;
    gap: 12px;
}

.desktop-additional-controls .desktop-control-btn {
    width: 40px;
    height: 40px;
}

.desktop-additional-controls .desktop-control-btn svg {
    width: 18px;
    height: 18px;
}

/* Desktop Sidebar auf Mobile verstecken */
@media (max-width: 768px) {
    .desktop-sidebar {
        display: none !important;
    }
}

/* Mobile Control Buttons auf Desktop anpassen */
@media (min-width: 769px) {
    .mobile-control-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-bottom: 20px;
        padding: 10px 0;
    }

    /* Alle Mobile-Control-Buttons auf Desktop anzeigen */
    .mobile-control-btn {
        width: 44px;
        height: 44px;
        background: #f8f9fa;
        color: #495057;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .mobile-control-btn:hover {
        background: #e9ecef;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .mobile-control-btn svg {
        width: 20px;
        height: 20px;
    }



    /* Desktop Button Text nur auf Desktop anzeigen */
    .desktop-btn-text {
        display: none;
    }

    @media (min-width: 769px) {
        .desktop-btn-text {
            display: inline;
        }
    }
}

/* Mobile Filter Toggle Button */
.mobile-filter-toggle-container {
    margin-bottom: 15px;
    padding: 0 5px;
}

.mobile-filter-toggle-btn {
    width: 100%;
    background: #f8f9fa;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    color: #112736;
    font-weight: 500;
}

.mobile-filter-toggle-btn:hover {
    background: #e9ecef;
    border-color: #4b879a;
}

.mobile-filter-toggle-btn.active {
    background: #e8f4f8;
    border-color: #4b879a;
    color: #4b879a;
}

.filter-toggle-icon {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
}

.mobile-filter-toggle-btn.active .filter-toggle-icon {
    transform: rotate(180deg);
}

/* Mobile Filter Controls */
.mobile-filter-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
    padding: 0 5px;
    overflow: hidden;
    transition: all 0.3s ease;
}

/* Auf Desktop horizontales Layout für Filter */
@media (min-width: 769px) {
    .mobile-filter-controls {
        flex-direction: row;
        gap: 15px;
        align-items: center;
        flex-wrap: wrap;
    }

    .mobile-filter-controls .filter-select {
        width: auto;
        min-width: 150px;
        flex: 1;
        max-width: 200px;
    }

    /* Filter-Apply-Group auf Desktop horizontal */
    .filter-apply-group {
        flex-direction: row;
        margin-top: 0;
        gap: 10px;
    }


}

.mobile-filter-controls.show {
    display: flex;
}

.mobile-filter-controls .filter-select {
    width: 100%;
    font-size: 16px; /* Verhindert Zoom auf iOS */
}



.mobile-control-buttons {
    display: flex;
    justify-content: center;
    gap: 15px; /* Etwas weniger Gap für 6 Buttons */
    margin-bottom: 20px;
    padding: 0 5px;
    flex-wrap: wrap; /* Erlaubt Umbruch bei sehr kleinen Bildschirmen */
}

.mobile-control-btn {
    background: #f8f9fa;
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #112736;
}

.mobile-control-btn:hover {
    background: #e9ecef;
    transform: scale(1.05);
}

.mobile-control-btn.active {
    background: #4b879a;
    color: white;
}

.mobile-control-btn.single-repeat {
    position: relative;
}

.mobile-control-btn.single-repeat .repeat-one {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Mobile Play Button hervorheben */
#mobile-play-filtered-btn {
    background: #4b879a;
    color: white;
}

#mobile-play-filtered-btn:hover {
    background: #3a6b7a;
    color: white;
}

/* Desktop Song Menu */
.desktop-song-menu {
    position: relative;
    display: inline-block;
}

.desktop-menu-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    color: #666;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.desktop-menu-btn:hover {
    background: #f0f0f0;
    color: #112736;
}

.desktop-menu-btn svg {
    width: 16px;
    height: 16px;
}

.desktop-menu-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 200px;
    overflow: hidden;
}

.desktop-menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    font-size: 14px;
    color: #333;
}

.desktop-menu-item:hover {
    background: #f8f9fa;
}

.desktop-menu-item:first-child {
    border-radius: 8px 8px 0 0;
}

.desktop-menu-item:last-child {
    border-radius: 0 0 8px 8px;
}

.desktop-menu-item svg {
    width: 16px;
    height: 16px;
    color: #666;
    flex-shrink: 0;
}

.desktop-menu-item span {
    flex: 1;
}

/* Actions Column */
.actions-column {
    width: 60px;
    text-align: center;
}

.actions-cell {
    text-align: center;
}

/* Filter Dropdowns */
.filter-controls {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 200px;
}

.filter-label {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.filter-dropdown {
    position: relative;
}

.filter-dropdown-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 12px 16px;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    transition: all 0.3s ease;
}

.filter-dropdown-btn:hover {
    border-color: #4b879a;
    background: #f8f9fa;
}

.filter-dropdown-btn:focus {
    outline: none;
    border-color: #4b879a;
    box-shadow: 0 0 0 3px rgba(75, 135, 154, 0.1);
}

.filter-text {
    flex: 1;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.filter-arrow {
    width: 16px;
    height: 16px;
    color: #666;
    transition: transform 0.3s ease;
    flex-shrink: 0;
    margin-left: 8px;
}

.filter-dropdown-content {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    display: none;
}



.filter-options {
    padding: 8px 0;
}

.filter-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.filter-option:hover {
    background: #f8f9fa;
}

.filter-option input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
}

.filter-option label {
    flex: 1;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    margin: 0;
}

/* "Alle [Kategorie]" Option Styling */
.filter-all-option {
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 8px;
    padding-bottom: 8px;
}

.filter-all-label {
    font-weight: 600;
    color: #4b879a !important;
}

.filter-separator {
    height: 1px;
    background: #e0e0e0;
    margin: 8px 0;
}

/* Mobile Filter Anpassungen */
.mobile-filter-controls {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    margin-bottom: 20px;
    position: relative;
    overflow: visible;
}

.mobile-filter-controls .filter-controls {
    flex-direction: column;
    gap: 16px;
}

.mobile-filter-controls .filter-group {
    min-width: auto;
    position: relative;
    overflow: visible;
}

.mobile-filter-controls .filter-dropdown {
    position: relative;
    overflow: visible;
}

.mobile-filter-controls .filter-dropdown-btn {
    padding: 14px 16px;
    font-size: 16px;
}

/* Mobile Dropdown-Spezifische Anpassungen */
.mobile-filter-controls .filter-dropdown-content {
    max-height: 250px;
    z-index: 9999;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
}

/* Filter Anwenden Button */
.filter-apply-group {
    display: flex;
    align-items: flex-end;
    margin-left: 20px;
}

.filter-apply-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.filter-apply-btn:hover {
    background: #218838;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.filter-apply-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.filter-apply-btn svg {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
}

.filter-apply-btn span {
    white-space: nowrap;
}

/* Mobile Filter Buttons */
.filter-apply-group {
    display: flex;
    gap: 8px;
    margin-top: 16px;
}

.mobile-filter-apply-btn {
    flex: 1;
    justify-content: center;
    padding: 12px 16px; /* Reduziertes Padding */
    font-size: 15px; /* Etwas kleinere Schrift */
    min-width: 0; /* Erlaubt Schrumpfen */
    white-space: nowrap; /* Verhindert Zeilenumbruch im Text */
}

.mobile-filter-apply-btn svg {
    width: 18px; /* Etwas kleinere Icons */
    height: 18px;
}



/* Responsive Anpassungen für Filter-Buttons */
@media (max-width: 480px) {
    .mobile-filter-apply-btn {
        padding: 10px 12px; /* Noch kompakter auf sehr kleinen Bildschirmen */
        font-size: 14px;
    }

    .mobile-filter-apply-btn svg {
        width: 16px;
        height: 16px;
    }


}

/* Responsive Anpassungen */
@media (max-width: 768px) {
    .filter-controls {
        flex-direction: column;
        gap: 16px;
    }

    .filter-group {
        min-width: auto;
    }

    .filter-apply-group {
        margin-left: 0;
        margin-top: 8px;
    }
}

.mobile-control-btn svg {
    width: 20px;
    height: 20px;
}

/* Responsive Anpassungen für mobile Control Buttons */
@media (max-width: 480px) {
    .mobile-control-buttons {
        gap: 10px; /* Noch weniger Gap bei sehr kleinen Bildschirmen */
    }

    .mobile-control-btn {
        width: 40px;
        height: 40px;
    }

    .mobile-control-btn svg {
        width: 18px;
        height: 18px;
    }
}

/* Filter Status Display */
.filter-status {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 10px 15px;
    margin: 15px 0;
    font-size: 14px;
    color: #495057;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.filter-status-text {
    font-weight: 500;
    flex: 1;
    text-align: center;
}

.filter-status-reset {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    margin-left: 10px;
    flex-shrink: 0;
}

.filter-status-reset:hover {
    background: #e9ecef;
    color: #495057;
}

.filter-status-reset svg {
    width: 16px;
    height: 16px;
}

/* Klickbare Filteranzeige für ausgeblendete Songs */
.filter-status[style*="cursor: pointer"] {
    transition: background-color 0.2s ease;
}

.filter-status[style*="cursor: pointer"]:hover {
    background: #e9ecef;
}

/* Mobile Song List - optimiert für alle Bildschirmgrößen */
.mobile-song-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.3s ease;
    position: relative;
    z-index: 1;
}

/* Auf Desktop etwas mehr Padding und Abstand */
@media (min-width: 769px) {
    .mobile-song-item {
        padding: 20px;
        margin-bottom: 12px;
        box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
    }
}

.mobile-song-item:hover {
    transform: translateY(-2px);
}

.mobile-song-item.playing {
    background: #e8f4f8;
    border-left: 4px solid #4b879a;
}

/* Song-Item mit geöffnetem Menü hat höchste Priorität */
.mobile-song-item.menu-open {
    z-index: 10001 !important;
}

.mobile-song-content {
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
}

.mobile-album-cover {
    width: 50px;
    height: 50px;
    border-radius: 6px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    overflow: hidden;
}

/* Auf Desktop größere Album-Cover */
@media (min-width: 769px) {
    .mobile-album-cover {
        width: 60px;
        height: 60px;
        border-radius: 8px;
    }

    .mobile-album-cover .placeholder-icon {
        width: 28px;
        height: 28px;
    }
}

.mobile-album-cover.has-cover {
    background: #f0f0f0;
}

/* Album-Cover Bilder - vollständig anzeigen wie im Modal */
.mobile-album-cover img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: inherit;
}

.mobile-album-cover .placeholder-icon {
    width: 24px;
    height: 24px;
    color: #999;
}

.mobile-song-info {
    flex: 1;
    min-width: 0;
}

.mobile-song-title {
    font-weight: 600;
    color: #112736;
    font-size: 16px;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.mobile-song-album {
    color: #666;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Auf Desktop größere Schrift */
@media (min-width: 769px) {
    .mobile-song-title {
        font-size: 18px;
        margin-bottom: 6px;
    }

    .mobile-song-album {
        font-size: 15px;
    }
}

/* Mobile Song Menu */
.mobile-song-menu {
    position: relative;
    margin-left: auto;
    z-index: 10000;
}

.mobile-menu-btn {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
    color: #666;
}

.mobile-menu-btn:hover {
    background: #f0f0f0;
}

.mobile-menu-btn svg {
    width: 20px;
    height: 20px;
}

.mobile-menu-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #e0e0e0;
    min-width: 180px;
    z-index: 9999;
    display: none;
}

/* Sicherstellen, dass das Menü auch bei Overflow sichtbar bleibt */
@media (max-width: 480px) {
    .mobile-menu-dropdown {
        right: -10px;
        min-width: 200px;
    }
}

.mobile-menu-dropdown.show {
    display: block !important;
}

.mobile-menu-item {
    width: 100%;
    background: none;
    border: none;
    padding: 12px 16px;
    text-align: left;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    color: #112736;
    transition: background-color 0.3s ease;
}

.mobile-menu-item:hover {
    background: #f8f9fa;
}

.mobile-menu-item:first-child {
    border-radius: 8px 8px 0 0;
}

.mobile-menu-item:last-child {
    border-radius: 0 0 8px 8px;
}

.mobile-menu-item svg {
    width: 16px;
    height: 16px;
    color: #666;
    flex-shrink: 0;
}

.mobile-menu-item span {
    flex: 1;
}

/* Mini Player */
.mini-player {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e0e0e0;
    padding: 10px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 1000;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.mini-player-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    cursor: pointer;
    padding: 5px;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.mini-player-info:hover {
    background: rgba(75, 135, 154, 0.1);
}

.mini-album-cover {
    width: 40px;
    height: 40px;
    background: #f0f0f0;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    transition: all 0.3s ease;
}

.mini-album-cover.has-cover {
    background: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mini-album-cover svg {
    width: 20px;
    height: 20px;
    color: #999;
}

.mini-song-info {
    flex: 1;
}

.mini-song-title {
    font-weight: 600;
    color: #112736;
    font-size: 14px;
    display: block;
}

.mini-artist {
    color: #666;
    font-size: 12px;
}

.mini-controls {
    display: flex;
    gap: 10px;
}

.mini-control-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
    color: #112736;
}

.mini-control-btn:hover {
    background: #f0f0f0;
}

.mini-control-btn svg {
    width: 20px;
    height: 20px;
}

/* Modal Player - New Design */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.modal-player {
    background: white;
    width: 100%;
    max-width: 400px;
    max-height: 95vh; /* Mehr Höhe erlauben */
    height: auto; /* Flexible Höhe */
    min-height: 400px; /* Reduzierte Mindesthöhe für mehr Flexibilität */
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

/* Close Button - Outside modal on overlay */
.modal-close {
    position: fixed;
    top: 30px;
    right: 30px;
    background: rgba(0, 0, 0, 0.8);
    border: none;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: all 0.3s ease;
    z-index: 2001; /* Höher als das Modal */
    backdrop-filter: blur(10px);
}

.modal-close:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

.modal-close svg {
    width: 24px;
    height: 24px;
    color: white;
}

/* Upper Section - Album Cover Area */
.modal-upper-section {
    height: auto; /* Flexible Höhe - passt sich dem Bild an */
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: visible; /* Erlaubt dem Bild, vollständig sichtbar zu sein */
    flex: 1; /* Nimmt verfügbaren Platz ein */
}

/* Auf mobilen Geräten das Modal anpassen */
@media (max-width: 768px) {
    .modal-player {
        max-height: 98vh; /* Fast die gesamte Bildschirmhöhe nutzen */
        height: auto;
        min-height: 300px; /* Noch geringere Mindesthöhe auf mobilen Geräten */
    }

    .modal-upper-section {
        height: auto; /* Flexible Höhe - passt sich dem Bild an */
        max-height: none; /* Keine Höhenbegrenzung */
        min-height: unset; /* Keine Mindesthöhe */
        flex: 1; /* Nimmt verfügbaren Platz ein */
    }

    .modal-lower-section {
        padding: 12px; /* Noch kompakteres Padding auf mobilen Geräten */
        flex-shrink: 1; /* Kann schrumpfen wenn nötig */
    }

    .modal-player-controls {
        gap: 16px; /* Etwas mehr Platz auch auf mobilen Geräten */
    }

    .modal-control-buttons {
        gap: 20px; /* Mehr Weißraum zwischen den Buttons */
    }

    /* Close Button für mobile Geräte anpassen */
    .modal-close {
        top: 20px;
        right: 20px;
        padding: 8px;
    }

    /* Tab-Navigation für mobile Geräte - mittlere Schriftgröße */
    .modal-tab {
        padding: 8px 12px; /* Etwas mehr Padding für bessere Lesbarkeit */
        font-size: 15px; /* Mittlere Schriftgröße zwischen alt und neu */
        max-width: 100px; /* Etwas mehr Breite für größere Schrift */
        min-width: 60px;
    }
}

/* Tab Navigation */
.modal-tab-navigation {
    position: relative; /* Ändere von absolute zu relative */
    bottom: auto; /* Entferne absolute Positionierung */
    left: auto;
    right: auto;
    display: flex;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 10px 15px; /* Größeres Padding für bessere Verteilung */
    gap: 8px; /* Größerer Gap für bessere Trennung */
    z-index: 5;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    justify-content: space-evenly; /* Gleichmäßige Verteilung mit Abstand */
    margin-top: auto; /* Schiebt die Navigation nach unten */
    flex-shrink: 0; /* Verhindert Schrumpfen */
}

.modal-tab {
    background: transparent;
    border: none;
    padding: 12px 20px; /* Etwas mehr Padding für bessere Lesbarkeit */
    border-radius: 0;
    font-size: 17px; /* Mittlere Schriftgröße zwischen alt und neu */
    font-weight: 500;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    position: relative;
    flex: 1;
    max-width: 150px; /* Mehr Platz für bessere Verteilung */
    min-width: 80px;
    text-align: center;
}

.modal-tab::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: transparent;
    transition: background-color 0.3s ease;
}

.modal-tab:hover {
    color: #4b879a;
    background: rgba(75, 135, 154, 0.05);
}

.modal-tab.active {
    background: transparent;
    color: #4b879a;
    font-weight: 600;
}

.modal-tab.active::after {
    background-color: #4b879a;
}

.modal-tab[style*="display: none"] {
    display: none !important;
}

/* Tab Content Area */
.modal-tab-content-area {
    flex: 1;
    position: relative;
    overflow: hidden; /* Standard für Lyrics/Info Tabs */
    display: flex;
    flex-direction: column;
}

/* Spezielle Behandlung wenn Cover-Tab aktiv ist */
.modal-tab-content-area.cover-active {
    overflow: visible; /* Erlaubt dem Cover-Bild, vollständig sichtbar zu sein */
}

.modal-tab-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0;
}

.modal-tab-content.active {
    opacity: 1;
    transform: translateX(0);
}

.modal-tab-content.slide-left {
    transform: translateX(-100%);
}

/* Album Cover Tab */
.modal-tab-content[data-content="cover"] {
    background: #f0f0f0;
    position: static !important; /* Überschreibt die absolute Positionierung */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    overflow: visible; /* Erlaubt vollständige Bildanzeige */
    height: auto; /* Flexible Höhe */
    opacity: 1 !important; /* Immer sichtbar wenn aktiv */
    transform: none !important; /* Keine Transformation */
    flex: 1; /* Nimmt verfügbaren Platz ein */
    min-height: 0; /* Erlaubt minimale Höhe */
}

.modal-tab-content[data-content="cover"] img {
    width: 100%;
    height: auto; /* Natürliche Höhe des Bildes */
    object-fit: contain; /* Vollständiges Bild anzeigen */
    border-radius: 0;
    max-height: none; /* Keine Höhenbegrenzung */
    display: block; /* Bessere Bilddarstellung */
}

.modal-album-cover {
    display: none; /* Das separate Cover-Element wird nicht mehr benötigt */
}

/* Placeholder für Cover-Tab wenn kein Bild vorhanden */
.modal-tab-content[data-content="cover"]:not(.has-cover) {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f0f0;
}

.modal-tab-content[data-content="cover"] .placeholder-icon {
    width: 80px;
    height: 80px;
    color: #999;
}

/* Song Info entfernt - wird nicht mehr angezeigt */
.modal-song-info {
    display: none;
}

/* Lyrics and Translation Tabs - Petrol-Blau Farbverlauf */
.modal-tab-content[data-content="lyrics"],
.modal-tab-content[data-content="translation"] {
    background: linear-gradient(135deg, #4b879a 0%, #3a6b7a 25%, #2c5f6f 50%, #1e4a56 75%, #0f3540 100%);
    justify-content: flex-start;
    padding: 30px;
    overflow-y: auto;
    position: absolute !important;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* Tab Content Titel - Einfaches Design */
.tab-content-title {
    margin: 0 0 25px 0;
    padding: 0;
    font-size: 22px;
    font-weight: 700;
    color: white;
    text-align: center;
}

/* Lyrics und Translation Text - Website-Style */
.modal-tab-content[data-content="lyrics"] p,
.modal-tab-content[data-content="translation"] p {
    color: white;
    font-size: 18px;
    line-height: 1.6; /* Kleinere Absätze */
    text-align: center;
    margin: 0;
    padding: 0;
    font-weight: 400;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    white-space: pre-line; /* Behält Zeilenumbrüche bei */
}

/* Keine Lyrics/Übersetzung verfügbar - Styling */
.modal-tab-content[data-content="lyrics"] .no-lyrics,
.modal-tab-content[data-content="translation"] .no-lyrics {
    color: rgba(255, 255, 255, 0.8);
    font-style: italic;
    font-size: 16px;
    text-align: center;
    margin-top: 50px;
}

/* Lyrics Content wird nicht mehr als separate Box benötigt */
.modal-lyrics-content {
    display: none;
}

.modal-lyrics-content p {
    margin-bottom: 15px;
    margin-top: 0;
}

.modal-lyrics-content p:last-child {
    margin-bottom: 0;
}

.no-lyrics {
    color: #666;
    font-style: italic;
    text-align: center;
}

/* Info Tab - Petrol-Blau Farbverlauf, kompakter */
.modal-tab-content[data-content="info"] {
    background: linear-gradient(135deg, #4b879a 0%, #3a6b7a 25%, #2c5f6f 50%, #1e4a56 75%, #0f3540 100%);
    justify-content: flex-start; /* Von oben beginnen */
    align-items: center; /* Horizontal zentriert */
    padding: 25px; /* Kompakter */
    overflow-y: auto;
    position: absolute !important;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* Info Container für zentrierte Anordnung */
.modal-tab-content[data-content="info"] .info-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start; /* Linksbündig untereinander */
    max-width: 300px;
    width: 100%;
    margin-top: 0; /* Sicherstellen, dass es von oben beginnt */
}

/* Info Tab Titel */
.modal-tab-content[data-content="info"] .tab-content-title {
    color: white; /* Weiß für dunklen Hintergrund */
    margin-bottom: 20px; /* Kompakter */
    text-align: center; /* Titel zentriert */
    width: 100%;
}

/* Info Content wird nicht mehr als separate Box benötigt */
.modal-info-content {
    display: none;
}

/* Direkte Info-Items in Tab-Content - Kompakteres Design */
.modal-tab-content[data-content="info"] .info-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 10px 0; /* Kompakter */
    border-bottom: 1px solid rgba(255, 255, 255, 0.3); /* Etwas sichtbarer */
}

/* Spezielle Behandlung für Interpretation-Items */
.modal-tab-content[data-content="info"] .info-item:has(.interpretation-text) {
    flex-direction: column; /* Interpretation untereinander statt nebeneinander */
    align-items: flex-start;
}

.modal-tab-content[data-content="info"] .info-item:has(.interpretation-text) .info-label {
    margin-bottom: 8px; /* Abstand zwischen Label und Text */
}

.modal-tab-content[data-content="info"] .info-item:last-child {
    border-bottom: none;
}

.modal-tab-content[data-content="info"] .info-label {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9); /* Weiß für dunklen Hintergrund */
    font-size: 15px; /* Etwas kleiner */
    min-width: 110px; /* Kompakter */
}

.modal-tab-content[data-content="info"] .info-value {
    color: white; /* Weiß für dunklen Hintergrund */
    font-size: 15px; /* Etwas kleiner */
    text-align: right;
    flex: 1;
    margin-left: 15px; /* Kompakter */
    word-break: break-word;
    line-height: 1.4; /* Kompakter */
}

/* Klickbare Info-Values (Album, Genre, Sprache) */
.modal-tab-content[data-content="info"] .info-value.clickable {
    background: rgba(255, 255, 255, 0.1);
    padding: 6px 12px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
    margin-left: 10px;
}

.modal-tab-content[data-content="info"] .info-value.clickable:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-1px);
}

/* Interpretation als Info-Item - Spezielles Styling */
.modal-tab-content[data-content="info"] .interpretation-text {
    color: white; /* Weiß für dunklen Hintergrund */
    font-size: 14px; /* Etwas kleiner */
    line-height: 1.5; /* Kompakter */
    text-align: left;
    word-break: break-word;
    max-width: 100%; /* Volle Breite für bessere Lesbarkeit */
    margin-left: 0; /* Überschreibt das margin-left vom .info-value */
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.info-label {
    font-weight: 600;
    color: #555;
    font-size: 13px;
    min-width: 80px;
}

.info-value {
    color: #333;
    font-size: 13px;
    text-align: right;
    flex: 1;
    margin-left: 15px;
    word-break: break-word;
}

.info-additional {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.info-description {
    font-size: 13px;
    line-height: 1.4;
    color: #666;
    margin: 0;
}

/* Lower Section - Player Controls */
.modal-lower-section {
    background: white;
    padding: 15px; /* Reduziertes Padding für mehr Platz */
    border-top: 1px solid #eee;
    flex-shrink: 1; /* Erlaubt Schrumpfen wenn nötig */
    min-height: 0; /* Erlaubt minimale Höhe */
}

/* Song Title Container */
.modal-song-title-container {
    text-align: center;
    margin-bottom: 20px;
    padding: 0 10px;
}

.modal-song-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0 0 5px 0;
    line-height: 1.3;
    word-wrap: break-word;
}

.modal-song-artist {
    display: none; /* Interpret wird nicht angezeigt */
}

.modal-player-controls {
    display: flex;
    flex-direction: column;
    gap: 20px; /* Mehr Platz zwischen den Button-Reihen und der Fortschrittsanzeige */
}

.modal-control-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 25px; /* Mehr Weißraum zwischen den Buttons */
}

.modal-control-btn {
    background: #f8f9fa;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #4b879a;
}

.modal-control-btn:hover {
    background: #e9ecef;
    transform: scale(1.05);
}

.modal-control-btn.active {
    background: #4b879a;
    color: white;
}

.modal-play-pause-btn {
    width: 60px;
    height: 60px;
    background: #4b879a;
    color: white;
}

.modal-play-pause-btn:hover {
    background: #3a6b7a;
    transform: scale(1.05);
}

.modal-control-btn svg {
    width: 24px;
    height: 24px;
}

.modal-play-pause-btn svg {
    width: 28px;
    height: 28px;
}

/* Progress Bar */
.modal-progress-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.modal-time-display {
    font-size: 12px;
    color: #666;
    font-weight: 500;
    min-width: 35px;
    text-align: center;
}

.modal-progress-bar-container {
    flex: 1;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    cursor: pointer;
    position: relative;
}

.modal-progress-bar {
    width: 100%;
    height: 100%;
    position: relative;
}

.modal-progress-fill {
    height: 100%;
    background: #4b879a;
    border-radius: 3px;
    width: 0%;
    transition: width 0.1s ease;
}

.modal-progress-handle {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translate(50%, -50%);
    width: 16px;
    height: 16px;
    background: #4b879a;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.modal-progress-bar-container:hover .modal-progress-handle {
    opacity: 1;
}

/* Additional Controls */
.modal-additional-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    padding-bottom: 20px; /* Mehr Platz unter den letzten Buttons */
}

/* Desktop Responsive Design */
@media (min-width: 769px) {
    .modal-overlay {
        padding: 40px;
    }

    .modal-player {
        max-width: 500px;
        max-height: 700px;
    }

    .modal-close {
        top: 40px;
        right: 40px;
        padding: 12px;
    }

    .modal-album-cover {
        width: 360px;
        height: 360px;
    }

    .modal-info-content {
        max-height: 50vh;
    }

    .modal-lyrics-content,
    .modal-info-content {
        max-width: 400px;
        padding: 40px;
    }

    .modal-control-btn {
        width: 55px;
        height: 55px;
    }

    .modal-play-pause-btn {
        width: 70px;
        height: 70px;
    }

    .modal-control-btn svg {
        width: 26px;
        height: 26px;
    }

    .modal-play-pause-btn svg {
        width: 32px;
        height: 32px;
    }
}

/* Touch Support - Swipe-Gesten komplett deaktiviert */
.modal-tab-content-area {
    touch-action: none; /* Alle Touch-Gesten deaktiviert */
    pointer-events: auto; /* Normale Klicks bleiben funktionsfähig */
}

.modal-tab-content {
    user-select: none;
    touch-action: none; /* Auch in den Inhalten keine Touch-Gesten */
}

/* Nur in Lyrics/Translation/Info Tabs vertikales Scrollen erlauben */
.modal-tab-content[data-content="lyrics"],
.modal-tab-content[data-content="translation"],
.modal-tab-content[data-content="info"] {
    touch-action: pan-y; /* Nur vertikales Scrollen in scrollbaren Inhalten */
}

/* Smooth scrolling for tab contents */
.modal-tab-content[data-content="lyrics"],
.modal-tab-content[data-content="translation"],
.modal-tab-content[data-content="info"] {
    scroll-behavior: smooth;
}

.modal-tab-content[data-content="lyrics"]::-webkit-scrollbar,
.modal-tab-content[data-content="translation"]::-webkit-scrollbar,
.modal-tab-content[data-content="info"]::-webkit-scrollbar {
    width: 6px;
}

.modal-tab-content[data-content="lyrics"]::-webkit-scrollbar-track,
.modal-tab-content[data-content="translation"]::-webkit-scrollbar-track,
.modal-tab-content[data-content="info"]::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.modal-tab-content[data-content="lyrics"]::-webkit-scrollbar-thumb,
.modal-tab-content[data-content="translation"]::-webkit-scrollbar-thumb,
.modal-tab-content[data-content="info"]::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
}

.modal-tab-content[data-content="lyrics"]::-webkit-scrollbar-thumb:hover,
.modal-tab-content[data-content="translation"]::-webkit-scrollbar-thumb:hover,
.modal-tab-content[data-content="info"]::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
}

/* Sehr kleine Bildschirme - Tab-Navigation minimal anpassen */
@media (max-width: 480px) {
    .modal-tab {
        padding: 8px 10px;
        font-size: 14px; /* Mittlere Schriftgröße auch bei sehr kleinen Bildschirmen */
        max-width: 80px;
        min-width: 50px;
    }
}

/* Responsive Design */




/* Mobile Layout wird jetzt oben definiert - diese Regel entfernt */

@media (max-width: 768px) {
    .mobile-layout {
        display: block !important;
        margin-top: 20px;
        overflow: visible;
        max-width: 800px;
        margin: 20px auto 0;
        padding: 0 20px;
        grid-template-columns: none !important;
    }
    
    .musik-title {
        font-size: 2rem;
    }
    
    .musik-description {
        font-size: 1rem;
        padding: 0 20px;
    }
    
    .container {
        padding: 0 20px;
    }
}

@media (max-width: 480px) {
    .musik-main {
        padding: 20px 0;
    }

    .musik-title {
        font-size: 1.8rem;
    }

    .breadcrumb-container {
        padding: 15px 0;
    }

    .container {
        padding: 0 15px;
    }

    .modal-overlay {
        padding: 10px;
    }

    .modal-player {
        max-width: 100%;
        max-height: 98vh; /* Fast die gesamte Bildschirmhöhe nutzen */
        height: auto;
        min-height: 250px; /* Sehr geringe Mindesthöhe für kleine Bildschirme */
    }

    /* Obere Sektion passt sich dem Bild an */
    .modal-upper-section {
        height: auto; /* Flexible Höhe */
        max-height: none; /* Keine Höhenbegrenzung */
        min-height: unset; /* Keine Mindesthöhe */
        flex: 1; /* Nimmt verfügbaren Platz ein */
    }

    /* Untere Sektion kompakter gestalten */
    .modal-lower-section {
        padding: 10px; /* Sehr reduziertes Padding */
        flex-shrink: 1; /* Kann schrumpfen */
    }

    /* Song Title Container für mobile Geräte anpassen */
    .modal-song-title-container {
        margin-bottom: 15px;
    }

    .modal-song-title {
        font-size: 18px;
    }

    .modal-song-artist {
        display: none; /* Interpret wird auch auf mobilen Geräten nicht angezeigt */
    }

    .modal-player-controls {
        gap: 10px; /* Sehr reduzierter Gap zwischen Elementen */
    }

    .modal-control-buttons {
        gap: 10px; /* Kompakte Button-Abstände */
    }

    .modal-control-buttons {
        gap: 15px; /* Reduzierter Gap zwischen Buttons */
    }

    /* Buttons etwas kleiner machen */
    .modal-control-btn {
        width: 45px;
        height: 45px;
    }

    .modal-play-pause-btn {
        width: 55px;
        height: 55px;
    }

    .modal-control-btn svg {
        width: 20px;
        height: 20px;
    }

    .modal-play-pause-btn svg {
        width: 26px;
        height: 26px;
    }

    .modal-album-cover {
        width: 280px;
        height: 280px;
    }

    .modal-info-content {
        max-height: 55vh;
        padding: 20px;
    }
}

/* Sehr kleine Bildschirme - weitere Optimierung für quadratisches Coverbild */
@media (max-width: 360px) {
    .modal-overlay {
        padding: 5px;
    }

    .modal-upper-section {
        height: auto; /* Flexible Höhe */
        max-height: none; /* Keine Höhenbegrenzung */
        min-height: unset; /* Keine Mindesthöhe */
        flex: 1; /* Nimmt verfügbaren Platz ein */
    }

    .modal-lower-section {
        padding: 8px; /* Minimal kompakt */
        flex-shrink: 1; /* Kann schrumpfen */
    }

    .modal-player-controls {
        gap: 8px; /* Minimale Abstände */
    }

    .modal-control-buttons {
        gap: 8px; /* Minimale Button-Abstände */
    }

    /* Buttons noch etwas kleiner */
    .modal-control-btn {
        width: 42px;
        height: 42px;
    }

    .modal-play-pause-btn {
        width: 50px;
        height: 50px;
    }

    .modal-control-btn svg {
        width: 18px;
        height: 18px;
    }

    .modal-play-pause-btn svg {
        width: 24px;
        height: 24px;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Loading States */
.song-table.loading tbody {
    opacity: 0.5;
}

.player-container.loading {
    opacity: 0.7;
}

/* Focus States for Accessibility */
.control-btn:focus,
.action-btn:focus,
.header-action-btn:focus {
    outline: 2px solid #4b879a;
    outline-offset: 2px;
}

.song-table tbody tr:focus {
    outline: 2px solid #4b879a;
    outline-offset: -2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .song-table th {
        border: 1px solid #000;
    }

    .song-table td {
        border: 1px solid #666;
    }

    .control-btn {
        border: 2px solid #000;
    }
}
